package com.radio.rangrez.service.coupon;

import com.radio.rangrez.dto.CreateCouponRequest;
import com.radio.rangrez.dto.CouponDto;
import com.radio.rangrez.dto.UpdateCouponPriorityRequest;

import java.util.List;

public interface CouponService {
    
    CouponDto createCoupon(CreateCouponRequest request);
    
    List<CouponDto> getAllCoupons();
    
    List<CouponDto> getActiveCoupons();
    
    List<CouponDto> getActiveAndValidCoupons();
    
    List<CouponDto> getExpiredCoupons();
    
    CouponDto getCouponById(Long id);
    
    CouponDto getCouponByCode(String couponCode);
    
    CouponDto updateCoupon(Long id, CreateCouponRequest request);

    CouponDto updateCouponPriority(Long id, UpdateCouponPriorityRequest request);

    boolean deleteCoupon(Long id);

    int expireAndDeactivateCoupons();
}

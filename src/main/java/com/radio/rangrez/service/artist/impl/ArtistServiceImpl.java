package com.radio.rangrez.service.artist.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.radio.rangrez.dto.ArtistDto;
import com.radio.rangrez.dto.ArtistGenreRequest;
import com.radio.rangrez.dto.ArtistPreferences;
import com.radio.rangrez.dto.GenreDto;
import com.radio.rangrez.exception.NotFoundException;
import com.radio.rangrez.exception.ServiceException;
import com.radio.rangrez.model.Artist;
import com.radio.rangrez.model.ArtistGenre;
import com.radio.rangrez.model.Genre;
import com.radio.rangrez.repository.ArtistGenreRepository;
import com.radio.rangrez.repository.ArtistRepository;
import com.radio.rangrez.repository.GenreRepository;
import com.radio.rangrez.service.artist.ArtistService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ArtistServiceImpl implements ArtistService {

    @Autowired
    private ArtistRepository artistRepository;

    @Autowired
    private ArtistGenreRepository artistGenreRepository;

    @Autowired
    private GenreRepository genreRepository;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public ArtistDto createArtist(ArtistDto artistDto) {
        Artist artist = new Artist();
        BeanUtils.copyProperties(artistDto, artist, "id", "created", "updated", "artistPreferences");

        // Handle ArtistPreferences conversion from object to JSON string
        if (artistDto.getArtistPreferences() != null) {
            try {
                String preferencesJson = objectMapper.writeValueAsString(artistDto.getArtistPreferences());
                artist.setArtistPreferences(preferencesJson);
            } catch (Exception e) {
                throw new ServiceException(ServiceException.ServiceExceptionType.JSON_CONVERSION_ERROR);
            }
        }

        Artist savedArtist = artistRepository.save(artist);
        return convertToDto(savedArtist);
    }

    @Override
    public List<ArtistDto> getAllArtists() {
        List<Artist> artists = artistRepository.findAll();
        return artists.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public ArtistDto getArtistById(Long id) {
        Artist artist =  artistRepository.findById(id)
                .orElseThrow(()->new NotFoundException(NotFoundException.NotFoundType.ARTIST_NOT_FOUND));
        return convertToDto(artist);
    }

    @Override
    public ArtistDto updateArtist(Long id, ArtistDto artistDto) {
        Optional<Artist> artistOptional = artistRepository.findById(id);
        if (artistOptional.isPresent()) {
            Artist existingArtist = artistOptional.get();

            // Perform partial update - only copy non-null fields
            copyNonNullProperties(artistDto, existingArtist);

            // Handle ArtistPreferences conversion from object to JSON string (only if not null)
            if (artistDto.getArtistPreferences() != null) {
                try {
                    String preferencesJson = objectMapper.writeValueAsString(artistDto.getArtistPreferences());
                    existingArtist.setArtistPreferences(preferencesJson);
                } catch (Exception e) {
                    throw new RuntimeException("Error converting artist preferences to JSON", e);
                }
            }

            Artist updatedArtist = artistRepository.save(existingArtist);
            return convertToDto(updatedArtist);
        }
        throw new NotFoundException(NotFoundException.NotFoundType.ARTIST_NOT_FOUND);
    }

    @Override
    public boolean deleteArtist(Long id) {
        Optional<Artist> artistOptional = artistRepository.findById(id);
        if (artistOptional.isPresent()) {
            Artist artist = artistOptional.get();
            artistRepository.delete(artist);
            return true;
        }
        throw new NotFoundException(NotFoundException.NotFoundType.ARTIST_NOT_FOUND);
    }

    private ArtistDto convertToDto(Artist artist) {
        ArtistDto dto = new ArtistDto();
        BeanUtils.copyProperties(artist, dto, "artistPreferences", "genres");

        // Handle ArtistPreferences conversion from JSON string to object
        if (artist.getArtistPreferences() != null && !artist.getArtistPreferences().isEmpty()) {
            try {
                ArtistPreferences preferences = objectMapper.readValue(artist.getArtistPreferences(), ArtistPreferences.class);
                dto.setArtistPreferences(preferences);
            } catch (Exception e) {
                // If JSON parsing fails, set empty preferences
                dto.setArtistPreferences(new ArtistPreferences());
            }
        } else {
            dto.setArtistPreferences(new ArtistPreferences());
        }

        // Load and convert genres
        List<Genre> genres = artistGenreRepository.findGenresByArtistId(artist.getId());
        List<GenreDto> genreDtos = genres.stream()
                .map(this::convertGenreToDto)
                .collect(Collectors.toList());
        dto.setGenres(genreDtos);

        return dto;
    }

    /**
     * Copy non-null properties from source DTO to target entity for partial updates
     * Excludes: id, created, updated, artistPreferences (handled separately)
     */
    private void copyNonNullProperties(ArtistDto source, Artist target) {
        if (source.getFullName() != null) {
            target.setFullName(source.getFullName());
        }
        if (source.getStageName() != null) {
            target.setStageName(source.getStageName());
        }
        if (source.getArtistImageUrl() != null) {
            target.setArtistImageUrl(source.getArtistImageUrl());
        }
        if (source.getBiography() != null) {
            target.setBiography(source.getBiography());
        }
        if (source.getCountryRegion() != null) {
            target.setCountryRegion(source.getCountryRegion());
        }
        if (source.getContactEmail() != null) {
            target.setContactEmail(source.getContactEmail());
        }
        if (source.getContactPhone() != null) {
            target.setContactPhone(source.getContactPhone());
        }
        if (source.getActivated() != null) {
            target.setActivated(source.getActivated());
        }
        // Note: artistPreferences is handled separately in the updateArtist method
        // Note: id, created, updated are intentionally excluded from updates
    }

    @Override
    public List<GenreDto> getArtistGenres(Long artistId) {
        List<Genre> genres = artistGenreRepository.findGenresByArtistId(artistId);
        return genres.stream()
                .map(this::convertGenreToDto)
                .collect(Collectors.toList());
    }

    @Override
    public ArtistDto addGenresToArtist(Long artistId, ArtistGenreRequest request) {
        Optional<Artist> artistOptional = artistRepository.findById(artistId);
        if (artistOptional.isPresent()) {
            Artist artist = artistOptional.get();

            for (Long genreId : request.getGenreIds()) {
                // Check if genre exists
                Optional<Genre> genreOptional = genreRepository.findById(genreId);
                if (genreOptional.isPresent() && !artistGenreRepository.existsByArtistIdAndGenreId(artistId, genreId)) {
                    ArtistGenre artistGenre = new ArtistGenre();
                    artistGenre.setArtist(artist);
                    artistGenre.setGenre(genreOptional.get());
                    artistGenreRepository.save(artistGenre);
                }
            }

            return convertToDto(artist);
        }
        throw new RuntimeException("Artist not found with id: " + artistId);
    }

    @Override
    public ArtistDto removeGenreFromArtist(Long artistId, Long genreId) {
        Optional<Artist> artistOptional = artistRepository.findById(artistId);
        if (artistOptional.isPresent()) {
            artistGenreRepository.deleteByArtistIdAndGenreId(artistId, genreId);
            return convertToDto(artistOptional.get());
        }
        throw new RuntimeException("Artist not found with id: " + artistId);
    }

    @Override
    public ArtistDto updateArtistGenres(Long artistId, ArtistGenreRequest request) {
        Optional<Artist> artistOptional = artistRepository.findById(artistId);
        if (artistOptional.isPresent()) {
            Artist artist = artistOptional.get();

            // Remove all existing genres
            artistGenreRepository.deleteByArtistId(artistId);

            // Add new genres
            for (Long genreId : request.getGenreIds()) {
                Optional<Genre> genreOptional = genreRepository.findById(genreId);
                if (genreOptional.isPresent()) {
                    ArtistGenre artistGenre = new ArtistGenre();
                    artistGenre.setArtist(artist);
                    artistGenre.setGenre(genreOptional.get());
                    artistGenreRepository.save(artistGenre);
                }
            }

            return convertToDto(artist);
        }
        throw new RuntimeException("Artist not found with id: " + artistId);
    }

    private GenreDto convertGenreToDto(Genre genre) {
        GenreDto dto = new GenreDto();
        BeanUtils.copyProperties(genre, dto);
        return dto;
    }

}
